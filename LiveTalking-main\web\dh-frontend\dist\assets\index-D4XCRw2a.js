(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const i of document.querySelectorAll('link[rel="modulepreload"]'))n(i);new MutationObserver(i=>{for(const r of i)if(r.type==="childList")for(const o of r.addedNodes)o.tagName==="LINK"&&o.rel==="modulepreload"&&n(o)}).observe(document,{childList:!0,subtree:!0});function s(i){const r={};return i.integrity&&(r.integrity=i.integrity),i.referrerPolicy&&(r.referrerPolicy=i.referrerPolicy),i.crossOrigin==="use-credentials"?r.credentials="include":i.crossOrigin==="anonymous"?r.credentials="omit":r.credentials="same-origin",r}function n(i){if(i.ep)return;i.ep=!0;const r=s(i);fetch(i.href,r)}})();/**
* @vue/shared v3.5.18
* (c) 2018-present <PERSON><PERSON> (<PERSON>) <PERSON> and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function Vs(e){const t=Object.create(null);for(const s of e.split(","))t[s]=1;return s=>s in t}const V={},nt=[],we=()=>{},qi=()=>!1,ts=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),Us=e=>e.startsWith("onUpdate:"),ne=Object.assign,Ks=(e,t)=>{const s=e.indexOf(t);s>-1&&e.splice(s,1)},Gi=Object.prototype.hasOwnProperty,j=(e,t)=>Gi.call(e,t),I=Array.isArray,it=e=>Mt(e)==="[object Map]",ss=e=>Mt(e)==="[object Set]",cn=e=>Mt(e)==="[object Date]",R=e=>typeof e=="function",q=e=>typeof e=="string",Se=e=>typeof e=="symbol",W=e=>e!==null&&typeof e=="object",Vn=e=>(W(e)||R(e))&&R(e.then)&&R(e.catch),Un=Object.prototype.toString,Mt=e=>Un.call(e),zi=e=>Mt(e).slice(8,-1),Kn=e=>Mt(e)==="[object Object]",Bs=e=>q(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,xt=Vs(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),ns=e=>{const t=Object.create(null);return s=>t[s]||(t[s]=e(s))},Yi=/-(\w)/g,Ue=ns(e=>e.replace(Yi,(t,s)=>s?s.toUpperCase():"")),Xi=/\B([A-Z])/g,Qe=ns(e=>e.replace(Xi,"-$1").toLowerCase()),Bn=ns(e=>e.charAt(0).toUpperCase()+e.slice(1)),gs=ns(e=>e?`on${Bn(e)}`:""),Ye=(e,t)=>!Object.is(e,t),Wt=(e,...t)=>{for(let s=0;s<e.length;s++)e[s](...t)},Es=(e,t,s,n=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:n,value:s})},Is=e=>{const t=parseFloat(e);return isNaN(t)?e:t};let fn;const is=()=>fn||(fn=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function rs(e){if(I(e)){const t={};for(let s=0;s<e.length;s++){const n=e[s],i=q(n)?tr(n):rs(n);if(i)for(const r in i)t[r]=i[r]}return t}else if(q(e)||W(e))return e}const Zi=/;(?![^(]*\))/g,Qi=/:([^]+)/,er=/\/\*[^]*?\*\//g;function tr(e){const t={};return e.replace(er,"").split(Zi).forEach(s=>{if(s){const n=s.split(Qi);n.length>1&&(t[n[0].trim()]=n[1].trim())}}),t}function Ve(e){let t="";if(q(e))t=e;else if(I(e))for(let s=0;s<e.length;s++){const n=Ve(e[s]);n&&(t+=n+" ")}else if(W(e))for(const s in e)e[s]&&(t+=s+" ");return t.trim()}const sr="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",nr=Vs(sr);function Wn(e){return!!e||e===""}function ir(e,t){if(e.length!==t.length)return!1;let s=!0;for(let n=0;s&&n<e.length;n++)s=os(e[n],t[n]);return s}function os(e,t){if(e===t)return!0;let s=cn(e),n=cn(t);if(s||n)return s&&n?e.getTime()===t.getTime():!1;if(s=Se(e),n=Se(t),s||n)return e===t;if(s=I(e),n=I(t),s||n)return s&&n?ir(e,t):!1;if(s=W(e),n=W(t),s||n){if(!s||!n)return!1;const i=Object.keys(e).length,r=Object.keys(t).length;if(i!==r)return!1;for(const o in e){const l=e.hasOwnProperty(o),f=t.hasOwnProperty(o);if(l&&!f||!l&&f||!os(e[o],t[o]))return!1}}return String(e)===String(t)}function kn(e,t){return e.findIndex(s=>os(s,t))}const $n=e=>!!(e&&e.__v_isRef===!0),yt=e=>q(e)?e:e==null?"":I(e)||W(e)&&(e.toString===Un||!R(e.toString))?$n(e)?yt(e.value):JSON.stringify(e,Jn,2):String(e),Jn=(e,t)=>$n(t)?Jn(e,t.value):it(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((s,[n,i],r)=>(s[bs(n,r)+" =>"]=i,s),{})}:ss(t)?{[`Set(${t.size})`]:[...t.values()].map(s=>bs(s))}:Se(t)?bs(t):W(t)&&!I(t)&&!Kn(t)?String(t):t,bs=(e,t="")=>{var s;return Se(e)?`Symbol(${(s=e.description)!=null?s:t})`:e};/**
* @vue/reactivity v3.5.18
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let oe;class rr{constructor(t=!1){this.detached=t,this._active=!0,this._on=0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=oe,!t&&oe&&(this.index=(oe.scopes||(oe.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,s;if(this.scopes)for(t=0,s=this.scopes.length;t<s;t++)this.scopes[t].pause();for(t=0,s=this.effects.length;t<s;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,s;if(this.scopes)for(t=0,s=this.scopes.length;t<s;t++)this.scopes[t].resume();for(t=0,s=this.effects.length;t<s;t++)this.effects[t].resume()}}run(t){if(this._active){const s=oe;try{return oe=this,t()}finally{oe=s}}}on(){++this._on===1&&(this.prevScope=oe,oe=this)}off(){this._on>0&&--this._on===0&&(oe=this.prevScope,this.prevScope=void 0)}stop(t){if(this._active){this._active=!1;let s,n;for(s=0,n=this.effects.length;s<n;s++)this.effects[s].stop();for(this.effects.length=0,s=0,n=this.cleanups.length;s<n;s++)this.cleanups[s]();if(this.cleanups.length=0,this.scopes){for(s=0,n=this.scopes.length;s<n;s++)this.scopes[s].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const i=this.parent.scopes.pop();i&&i!==this&&(this.parent.scopes[this.index]=i,i.index=this.index)}this.parent=void 0}}}function or(){return oe}let B;const ms=new WeakSet;class qn{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,oe&&oe.active&&oe.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,ms.has(this)&&(ms.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||zn(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,un(this),Yn(this);const t=B,s=ae;B=this,ae=!0;try{return this.fn()}finally{Xn(this),B=t,ae=s,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)$s(t);this.deps=this.depsTail=void 0,un(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?ms.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){Ps(this)&&this.run()}get dirty(){return Ps(this)}}let Gn=0,vt,wt;function zn(e,t=!1){if(e.flags|=8,t){e.next=wt,wt=e;return}e.next=vt,vt=e}function Ws(){Gn++}function ks(){if(--Gn>0)return;if(wt){let t=wt;for(wt=void 0;t;){const s=t.next;t.next=void 0,t.flags&=-9,t=s}}let e;for(;vt;){let t=vt;for(vt=void 0;t;){const s=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(n){e||(e=n)}t=s}}if(e)throw e}function Yn(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function Xn(e){let t,s=e.depsTail,n=s;for(;n;){const i=n.prevDep;n.version===-1?(n===s&&(s=i),$s(n),lr(n)):t=n,n.dep.activeLink=n.prevActiveLink,n.prevActiveLink=void 0,n=i}e.deps=t,e.depsTail=s}function Ps(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(Zn(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function Zn(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===Et)||(e.globalVersion=Et,!e.isSSR&&e.flags&128&&(!e.deps&&!e._dirty||!Ps(e))))return;e.flags|=2;const t=e.dep,s=B,n=ae;B=e,ae=!0;try{Yn(e);const i=e.fn(e._value);(t.version===0||Ye(i,e._value))&&(e.flags|=128,e._value=i,t.version++)}catch(i){throw t.version++,i}finally{B=s,ae=n,Xn(e),e.flags&=-3}}function $s(e,t=!1){const{dep:s,prevSub:n,nextSub:i}=e;if(n&&(n.nextSub=i,e.prevSub=void 0),i&&(i.prevSub=n,e.nextSub=void 0),s.subs===e&&(s.subs=n,!n&&s.computed)){s.computed.flags&=-5;for(let r=s.computed.deps;r;r=r.nextDep)$s(r,!0)}!t&&!--s.sc&&s.map&&s.map.delete(s.key)}function lr(e){const{prevDep:t,nextDep:s}=e;t&&(t.nextDep=s,e.prevDep=void 0),s&&(s.prevDep=t,e.nextDep=void 0)}let ae=!0;const Qn=[];function Me(){Qn.push(ae),ae=!1}function Fe(){const e=Qn.pop();ae=e===void 0?!0:e}function un(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const s=B;B=void 0;try{t()}finally{B=s}}}let Et=0;class cr{constructor(t,s){this.sub=t,this.dep=s,this.version=s.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class ei{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0,this.__v_skip=!0}track(t){if(!B||!ae||B===this.computed)return;let s=this.activeLink;if(s===void 0||s.sub!==B)s=this.activeLink=new cr(B,this),B.deps?(s.prevDep=B.depsTail,B.depsTail.nextDep=s,B.depsTail=s):B.deps=B.depsTail=s,ti(s);else if(s.version===-1&&(s.version=this.version,s.nextDep)){const n=s.nextDep;n.prevDep=s.prevDep,s.prevDep&&(s.prevDep.nextDep=n),s.prevDep=B.depsTail,s.nextDep=void 0,B.depsTail.nextDep=s,B.depsTail=s,B.deps===s&&(B.deps=n)}return s}trigger(t){this.version++,Et++,this.notify(t)}notify(t){Ws();try{for(let s=this.subs;s;s=s.prevSub)s.sub.notify()&&s.sub.dep.notify()}finally{ks()}}}function ti(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let n=t.deps;n;n=n.nextDep)ti(n)}const s=e.dep.subs;s!==e&&(e.prevSub=s,s&&(s.nextSub=e)),e.dep.subs=e}}const As=new WeakMap,Xe=Symbol(""),Rs=Symbol(""),It=Symbol("");function Y(e,t,s){if(ae&&B){let n=As.get(e);n||As.set(e,n=new Map);let i=n.get(s);i||(n.set(s,i=new ei),i.map=n,i.key=s),i.track()}}function Pe(e,t,s,n,i,r){const o=As.get(e);if(!o){Et++;return}const l=f=>{f&&f.trigger()};if(Ws(),t==="clear")o.forEach(l);else{const f=I(e),h=f&&Bs(s);if(f&&s==="length"){const a=Number(n);o.forEach((p,T)=>{(T==="length"||T===It||!Se(T)&&T>=a)&&l(p)})}else switch((s!==void 0||o.has(void 0))&&l(o.get(s)),h&&l(o.get(It)),t){case"add":f?h&&l(o.get("length")):(l(o.get(Xe)),it(e)&&l(o.get(Rs)));break;case"delete":f||(l(o.get(Xe)),it(e)&&l(o.get(Rs)));break;case"set":it(e)&&l(o.get(Xe));break}}ks()}function et(e){const t=H(e);return t===e?t:(Y(t,"iterate",It),de(e)?t:t.map(Q))}function ls(e){return Y(e=H(e),"iterate",It),e}const fr={__proto__:null,[Symbol.iterator](){return ys(this,Symbol.iterator,Q)},concat(...e){return et(this).concat(...e.map(t=>I(t)?et(t):t))},entries(){return ys(this,"entries",e=>(e[1]=Q(e[1]),e))},every(e,t){return Ce(this,"every",e,t,void 0,arguments)},filter(e,t){return Ce(this,"filter",e,t,s=>s.map(Q),arguments)},find(e,t){return Ce(this,"find",e,t,Q,arguments)},findIndex(e,t){return Ce(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return Ce(this,"findLast",e,t,Q,arguments)},findLastIndex(e,t){return Ce(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return Ce(this,"forEach",e,t,void 0,arguments)},includes(...e){return _s(this,"includes",e)},indexOf(...e){return _s(this,"indexOf",e)},join(e){return et(this).join(e)},lastIndexOf(...e){return _s(this,"lastIndexOf",e)},map(e,t){return Ce(this,"map",e,t,void 0,arguments)},pop(){return pt(this,"pop")},push(...e){return pt(this,"push",e)},reduce(e,...t){return an(this,"reduce",e,t)},reduceRight(e,...t){return an(this,"reduceRight",e,t)},shift(){return pt(this,"shift")},some(e,t){return Ce(this,"some",e,t,void 0,arguments)},splice(...e){return pt(this,"splice",e)},toReversed(){return et(this).toReversed()},toSorted(e){return et(this).toSorted(e)},toSpliced(...e){return et(this).toSpliced(...e)},unshift(...e){return pt(this,"unshift",e)},values(){return ys(this,"values",Q)}};function ys(e,t,s){const n=ls(e),i=n[t]();return n!==e&&!de(e)&&(i._next=i.next,i.next=()=>{const r=i._next();return r.value&&(r.value=s(r.value)),r}),i}const ur=Array.prototype;function Ce(e,t,s,n,i,r){const o=ls(e),l=o!==e&&!de(e),f=o[t];if(f!==ur[t]){const p=f.apply(e,r);return l?Q(p):p}let h=s;o!==e&&(l?h=function(p,T){return s.call(this,Q(p),T,e)}:s.length>2&&(h=function(p,T){return s.call(this,p,T,e)}));const a=f.call(o,h,n);return l&&i?i(a):a}function an(e,t,s,n){const i=ls(e);let r=s;return i!==e&&(de(e)?s.length>3&&(r=function(o,l,f){return s.call(this,o,l,f,e)}):r=function(o,l,f){return s.call(this,o,Q(l),f,e)}),i[t](r,...n)}function _s(e,t,s){const n=H(e);Y(n,"iterate",It);const i=n[t](...s);return(i===-1||i===!1)&&zs(s[0])?(s[0]=H(s[0]),n[t](...s)):i}function pt(e,t,s=[]){Me(),Ws();const n=H(e)[t].apply(e,s);return ks(),Fe(),n}const ar=Vs("__proto__,__v_isRef,__isVue"),si=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(Se));function dr(e){Se(e)||(e=String(e));const t=H(this);return Y(t,"has",e),t.hasOwnProperty(e)}class ni{constructor(t=!1,s=!1){this._isReadonly=t,this._isShallow=s}get(t,s,n){if(s==="__v_skip")return t.__v_skip;const i=this._isReadonly,r=this._isShallow;if(s==="__v_isReactive")return!i;if(s==="__v_isReadonly")return i;if(s==="__v_isShallow")return r;if(s==="__v_raw")return n===(i?r?wr:li:r?oi:ri).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(n)?t:void 0;const o=I(t);if(!i){let f;if(o&&(f=fr[s]))return f;if(s==="hasOwnProperty")return dr}const l=Reflect.get(t,s,se(t)?t:n);return(Se(s)?si.has(s):ar(s))||(i||Y(t,"get",s),r)?l:se(l)?o&&Bs(s)?l:l.value:W(l)?i?ci(l):qs(l):l}}class ii extends ni{constructor(t=!1){super(!1,t)}set(t,s,n,i){let r=t[s];if(!this._isShallow){const f=Ze(r);if(!de(n)&&!Ze(n)&&(r=H(r),n=H(n)),!I(t)&&se(r)&&!se(n))return f?!1:(r.value=n,!0)}const o=I(t)&&Bs(s)?Number(s)<t.length:j(t,s),l=Reflect.set(t,s,n,se(t)?t:i);return t===H(i)&&(o?Ye(n,r)&&Pe(t,"set",s,n):Pe(t,"add",s,n)),l}deleteProperty(t,s){const n=j(t,s);t[s];const i=Reflect.deleteProperty(t,s);return i&&n&&Pe(t,"delete",s,void 0),i}has(t,s){const n=Reflect.has(t,s);return(!Se(s)||!si.has(s))&&Y(t,"has",s),n}ownKeys(t){return Y(t,"iterate",I(t)?"length":Xe),Reflect.ownKeys(t)}}class hr extends ni{constructor(t=!1){super(!0,t)}set(t,s){return!0}deleteProperty(t,s){return!0}}const pr=new ii,gr=new hr,br=new ii(!0);const Ms=e=>e,Ut=e=>Reflect.getPrototypeOf(e);function mr(e,t,s){return function(...n){const i=this.__v_raw,r=H(i),o=it(r),l=e==="entries"||e===Symbol.iterator&&o,f=e==="keys"&&o,h=i[e](...n),a=s?Ms:t?qt:Q;return!t&&Y(r,"iterate",f?Rs:Xe),{next(){const{value:p,done:T}=h.next();return T?{value:p,done:T}:{value:l?[a(p[0]),a(p[1])]:a(p),done:T}},[Symbol.iterator](){return this}}}}function Kt(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function yr(e,t){const s={get(i){const r=this.__v_raw,o=H(r),l=H(i);e||(Ye(i,l)&&Y(o,"get",i),Y(o,"get",l));const{has:f}=Ut(o),h=t?Ms:e?qt:Q;if(f.call(o,i))return h(r.get(i));if(f.call(o,l))return h(r.get(l));r!==o&&r.get(i)},get size(){const i=this.__v_raw;return!e&&Y(H(i),"iterate",Xe),Reflect.get(i,"size",i)},has(i){const r=this.__v_raw,o=H(r),l=H(i);return e||(Ye(i,l)&&Y(o,"has",i),Y(o,"has",l)),i===l?r.has(i):r.has(i)||r.has(l)},forEach(i,r){const o=this,l=o.__v_raw,f=H(l),h=t?Ms:e?qt:Q;return!e&&Y(f,"iterate",Xe),l.forEach((a,p)=>i.call(r,h(a),h(p),o))}};return ne(s,e?{add:Kt("add"),set:Kt("set"),delete:Kt("delete"),clear:Kt("clear")}:{add(i){!t&&!de(i)&&!Ze(i)&&(i=H(i));const r=H(this);return Ut(r).has.call(r,i)||(r.add(i),Pe(r,"add",i,i)),this},set(i,r){!t&&!de(r)&&!Ze(r)&&(r=H(r));const o=H(this),{has:l,get:f}=Ut(o);let h=l.call(o,i);h||(i=H(i),h=l.call(o,i));const a=f.call(o,i);return o.set(i,r),h?Ye(r,a)&&Pe(o,"set",i,r):Pe(o,"add",i,r),this},delete(i){const r=H(this),{has:o,get:l}=Ut(r);let f=o.call(r,i);f||(i=H(i),f=o.call(r,i)),l&&l.call(r,i);const h=r.delete(i);return f&&Pe(r,"delete",i,void 0),h},clear(){const i=H(this),r=i.size!==0,o=i.clear();return r&&Pe(i,"clear",void 0,void 0),o}}),["keys","values","entries",Symbol.iterator].forEach(i=>{s[i]=mr(i,e,t)}),s}function Js(e,t){const s=yr(e,t);return(n,i,r)=>i==="__v_isReactive"?!e:i==="__v_isReadonly"?e:i==="__v_raw"?n:Reflect.get(j(s,i)&&i in n?s:n,i,r)}const _r={get:Js(!1,!1)},xr={get:Js(!1,!0)},vr={get:Js(!0,!1)};const ri=new WeakMap,oi=new WeakMap,li=new WeakMap,wr=new WeakMap;function Sr(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function Tr(e){return e.__v_skip||!Object.isExtensible(e)?0:Sr(zi(e))}function qs(e){return Ze(e)?e:Gs(e,!1,pr,_r,ri)}function Cr(e){return Gs(e,!1,br,xr,oi)}function ci(e){return Gs(e,!0,gr,vr,li)}function Gs(e,t,s,n,i){if(!W(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const r=Tr(e);if(r===0)return e;const o=i.get(e);if(o)return o;const l=new Proxy(e,r===2?n:s);return i.set(e,l),l}function rt(e){return Ze(e)?rt(e.__v_raw):!!(e&&e.__v_isReactive)}function Ze(e){return!!(e&&e.__v_isReadonly)}function de(e){return!!(e&&e.__v_isShallow)}function zs(e){return e?!!e.__v_raw:!1}function H(e){const t=e&&e.__v_raw;return t?H(t):e}function Or(e){return!j(e,"__v_skip")&&Object.isExtensible(e)&&Es(e,"__v_skip",!0),e}const Q=e=>W(e)?qs(e):e,qt=e=>W(e)?ci(e):e;function se(e){return e?e.__v_isRef===!0:!1}function Er(e){return se(e)?e.value:e}const Ir={get:(e,t,s)=>t==="__v_raw"?e:Er(Reflect.get(e,t,s)),set:(e,t,s,n)=>{const i=e[t];return se(i)&&!se(s)?(i.value=s,!0):Reflect.set(e,t,s,n)}};function fi(e){return rt(e)?e:new Proxy(e,Ir)}class Pr{constructor(t,s,n){this.fn=t,this.setter=s,this._value=void 0,this.dep=new ei(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=Et-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!s,this.isSSR=n}notify(){if(this.flags|=16,!(this.flags&8)&&B!==this)return zn(this,!0),!0}get value(){const t=this.dep.track();return Zn(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function Ar(e,t,s=!1){let n,i;return R(e)?n=e:(n=e.get,i=e.set),new Pr(n,i,s)}const Bt={},Gt=new WeakMap;let Ge;function Rr(e,t=!1,s=Ge){if(s){let n=Gt.get(s);n||Gt.set(s,n=[]),n.push(e)}}function Mr(e,t,s=V){const{immediate:n,deep:i,once:r,scheduler:o,augmentJob:l,call:f}=s,h=P=>i?P:de(P)||i===!1||i===0?Ae(P,1):Ae(P);let a,p,T,C,D=!1,F=!1;if(se(e)?(p=()=>e.value,D=de(e)):rt(e)?(p=()=>h(e),D=!0):I(e)?(F=!0,D=e.some(P=>rt(P)||de(P)),p=()=>e.map(P=>{if(se(P))return P.value;if(rt(P))return h(P);if(R(P))return f?f(P,2):P()})):R(e)?t?p=f?()=>f(e,2):e:p=()=>{if(T){Me();try{T()}finally{Fe()}}const P=Ge;Ge=a;try{return f?f(e,3,[C]):e(C)}finally{Ge=P}}:p=we,t&&i){const P=p,G=i===!0?1/0:i;p=()=>Ae(P(),G)}const z=or(),L=()=>{a.stop(),z&&z.active&&Ks(z.effects,a)};if(r&&t){const P=t;t=(...G)=>{P(...G),L()}}let $=F?new Array(e.length).fill(Bt):Bt;const J=P=>{if(!(!(a.flags&1)||!a.dirty&&!P))if(t){const G=a.run();if(i||D||(F?G.some((je,he)=>Ye(je,$[he])):Ye(G,$))){T&&T();const je=Ge;Ge=a;try{const he=[G,$===Bt?void 0:F&&$[0]===Bt?[]:$,C];$=G,f?f(t,3,he):t(...he)}finally{Ge=je}}}else a.run()};return l&&l(J),a=new qn(p),a.scheduler=o?()=>o(J,!1):J,C=P=>Rr(P,!1,a),T=a.onStop=()=>{const P=Gt.get(a);if(P){if(f)f(P,4);else for(const G of P)G();Gt.delete(a)}},t?n?J(!0):$=a.run():o?o(J.bind(null,!0),!0):a.run(),L.pause=a.pause.bind(a),L.resume=a.resume.bind(a),L.stop=L,L}function Ae(e,t=1/0,s){if(t<=0||!W(e)||e.__v_skip||(s=s||new Set,s.has(e)))return e;if(s.add(e),t--,se(e))Ae(e.value,t,s);else if(I(e))for(let n=0;n<e.length;n++)Ae(e[n],t,s);else if(ss(e)||it(e))e.forEach(n=>{Ae(n,t,s)});else if(Kn(e)){for(const n in e)Ae(e[n],t,s);for(const n of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,n)&&Ae(e[n],t,s)}return e}/**
* @vue/runtime-core v3.5.18
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function Ft(e,t,s,n){try{return n?e(...n):e()}catch(i){cs(i,t,s)}}function Te(e,t,s,n){if(R(e)){const i=Ft(e,t,s,n);return i&&Vn(i)&&i.catch(r=>{cs(r,t,s)}),i}if(I(e)){const i=[];for(let r=0;r<e.length;r++)i.push(Te(e[r],t,s,n));return i}}function cs(e,t,s,n=!0){const i=t?t.vnode:null,{errorHandler:r,throwUnhandledErrorInProduction:o}=t&&t.appContext.config||V;if(t){let l=t.parent;const f=t.proxy,h=`https://vuejs.org/error-reference/#runtime-${s}`;for(;l;){const a=l.ec;if(a){for(let p=0;p<a.length;p++)if(a[p](e,f,h)===!1)return}l=l.parent}if(r){Me(),Ft(r,null,10,[e,f,h]),Fe();return}}Fr(e,s,i,n,o)}function Fr(e,t,s,n=!0,i=!1){if(i)throw e;console.error(e)}const ee=[];let _e=-1;const ot=[];let He=null,st=0;const ui=Promise.resolve();let zt=null;function Dr(e){const t=zt||ui;return e?t.then(this?e.bind(this):e):t}function jr(e){let t=_e+1,s=ee.length;for(;t<s;){const n=t+s>>>1,i=ee[n],r=Pt(i);r<e||r===e&&i.flags&2?t=n+1:s=n}return t}function Ys(e){if(!(e.flags&1)){const t=Pt(e),s=ee[ee.length-1];!s||!(e.flags&2)&&t>=Pt(s)?ee.push(e):ee.splice(jr(t),0,e),e.flags|=1,ai()}}function ai(){zt||(zt=ui.then(hi))}function Nr(e){I(e)?ot.push(...e):He&&e.id===-1?He.splice(st+1,0,e):e.flags&1||(ot.push(e),e.flags|=1),ai()}function dn(e,t,s=_e+1){for(;s<ee.length;s++){const n=ee[s];if(n&&n.flags&2){if(e&&n.id!==e.uid)continue;ee.splice(s,1),s--,n.flags&4&&(n.flags&=-2),n(),n.flags&4||(n.flags&=-2)}}}function di(e){if(ot.length){const t=[...new Set(ot)].sort((s,n)=>Pt(s)-Pt(n));if(ot.length=0,He){He.push(...t);return}for(He=t,st=0;st<He.length;st++){const s=He[st];s.flags&4&&(s.flags&=-2),s.flags&8||s(),s.flags&=-2}He=null,st=0}}const Pt=e=>e.id==null?e.flags&2?-1:1/0:e.id;function hi(e){try{for(_e=0;_e<ee.length;_e++){const t=ee[_e];t&&!(t.flags&8)&&(t.flags&4&&(t.flags&=-2),Ft(t,t.i,t.i?15:14),t.flags&4||(t.flags&=-2))}}finally{for(;_e<ee.length;_e++){const t=ee[_e];t&&(t.flags&=-2)}_e=-1,ee.length=0,di(),zt=null,(ee.length||ot.length)&&hi()}}let ue=null,pi=null;function Yt(e){const t=ue;return ue=e,pi=e&&e.type.__scopeId||null,t}function Hr(e,t=ue,s){if(!t||e._n)return e;const n=(...i)=>{n._d&&vn(-1);const r=Yt(t);let o;try{o=e(...i)}finally{Yt(r),n._d&&vn(1)}return o};return n._n=!0,n._c=!0,n._d=!0,n}function tt(e,t){if(ue===null)return e;const s=ds(ue),n=e.dirs||(e.dirs=[]);for(let i=0;i<t.length;i++){let[r,o,l,f=V]=t[i];r&&(R(r)&&(r={mounted:r,updated:r}),r.deep&&Ae(o),n.push({dir:r,instance:s,value:o,oldValue:void 0,arg:l,modifiers:f}))}return e}function $e(e,t,s,n){const i=e.dirs,r=t&&t.dirs;for(let o=0;o<i.length;o++){const l=i[o];r&&(l.oldValue=r[o].value);let f=l.dir[n];f&&(Me(),Te(f,s,8,[e.el,l,e,t]),Fe())}}const Lr=Symbol("_vte"),Vr=e=>e.__isTeleport;function Xs(e,t){e.shapeFlag&6&&e.component?(e.transition=t,Xs(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}function gi(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function St(e,t,s,n,i=!1){if(I(e)){e.forEach((D,F)=>St(D,t&&(I(t)?t[F]:t),s,n,i));return}if(Tt(n)&&!i){n.shapeFlag&512&&n.type.__asyncResolved&&n.component.subTree.component&&St(e,t,s,n.component.subTree);return}const r=n.shapeFlag&4?ds(n.component):n.el,o=i?null:r,{i:l,r:f}=e,h=t&&t.r,a=l.refs===V?l.refs={}:l.refs,p=l.setupState,T=H(p),C=p===V?()=>!1:D=>j(T,D);if(h!=null&&h!==f&&(q(h)?(a[h]=null,C(h)&&(p[h]=null)):se(h)&&(h.value=null)),R(f))Ft(f,l,12,[o,a]);else{const D=q(f),F=se(f);if(D||F){const z=()=>{if(e.f){const L=D?C(f)?p[f]:a[f]:f.value;i?I(L)&&Ks(L,r):I(L)?L.includes(r)||L.push(r):D?(a[f]=[r],C(f)&&(p[f]=a[f])):(f.value=[r],e.k&&(a[e.k]=f.value))}else D?(a[f]=o,C(f)&&(p[f]=o)):F&&(f.value=o,e.k&&(a[e.k]=o))};o?(z.id=-1,ce(z,s)):z()}}}is().requestIdleCallback;is().cancelIdleCallback;const Tt=e=>!!e.type.__asyncLoader,bi=e=>e.type.__isKeepAlive;function Ur(e,t){mi(e,"a",t)}function Kr(e,t){mi(e,"da",t)}function mi(e,t,s=te){const n=e.__wdc||(e.__wdc=()=>{let i=s;for(;i;){if(i.isDeactivated)return;i=i.parent}return e()});if(fs(t,n,s),s){let i=s.parent;for(;i&&i.parent;)bi(i.parent.vnode)&&Br(n,t,s,i),i=i.parent}}function Br(e,t,s,n){const i=fs(t,e,n,!0);yi(()=>{Ks(n[t],i)},s)}function fs(e,t,s=te,n=!1){if(s){const i=s[e]||(s[e]=[]),r=t.__weh||(t.__weh=(...o)=>{Me();const l=Dt(s),f=Te(t,s,e,o);return l(),Fe(),f});return n?i.unshift(r):i.push(r),r}}const De=e=>(t,s=te)=>{(!Rt||e==="sp")&&fs(e,(...n)=>t(...n),s)},Wr=De("bm"),kr=De("m"),$r=De("bu"),Jr=De("u"),qr=De("bum"),yi=De("um"),Gr=De("sp"),zr=De("rtg"),Yr=De("rtc");function Xr(e,t=te){fs("ec",e,t)}const Zr=Symbol.for("v-ndc");function Qr(e,t,s,n){let i;const r=s,o=I(e);if(o||q(e)){const l=o&&rt(e);let f=!1,h=!1;l&&(f=!de(e),h=Ze(e),e=ls(e)),i=new Array(e.length);for(let a=0,p=e.length;a<p;a++)i[a]=t(f?h?qt(Q(e[a])):Q(e[a]):e[a],a,void 0,r)}else if(typeof e=="number"){i=new Array(e);for(let l=0;l<e;l++)i[l]=t(l+1,l,void 0,r)}else if(W(e))if(e[Symbol.iterator])i=Array.from(e,(l,f)=>t(l,f,void 0,r));else{const l=Object.keys(e);i=new Array(l.length);for(let f=0,h=l.length;f<h;f++){const a=l[f];i[f]=t(e[a],a,f,r)}}else i=[];return i}const Fs=e=>e?Ui(e)?ds(e):Fs(e.parent):null,Ct=ne(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>Fs(e.parent),$root:e=>Fs(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>xi(e),$forceUpdate:e=>e.f||(e.f=()=>{Ys(e.update)}),$nextTick:e=>e.n||(e.n=Dr.bind(e.proxy)),$watch:e=>wo.bind(e)}),xs=(e,t)=>e!==V&&!e.__isScriptSetup&&j(e,t),eo={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:s,setupState:n,data:i,props:r,accessCache:o,type:l,appContext:f}=e;let h;if(t[0]!=="$"){const C=o[t];if(C!==void 0)switch(C){case 1:return n[t];case 2:return i[t];case 4:return s[t];case 3:return r[t]}else{if(xs(n,t))return o[t]=1,n[t];if(i!==V&&j(i,t))return o[t]=2,i[t];if((h=e.propsOptions[0])&&j(h,t))return o[t]=3,r[t];if(s!==V&&j(s,t))return o[t]=4,s[t];Ds&&(o[t]=0)}}const a=Ct[t];let p,T;if(a)return t==="$attrs"&&Y(e.attrs,"get",""),a(e);if((p=l.__cssModules)&&(p=p[t]))return p;if(s!==V&&j(s,t))return o[t]=4,s[t];if(T=f.config.globalProperties,j(T,t))return T[t]},set({_:e},t,s){const{data:n,setupState:i,ctx:r}=e;return xs(i,t)?(i[t]=s,!0):n!==V&&j(n,t)?(n[t]=s,!0):j(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(r[t]=s,!0)},has({_:{data:e,setupState:t,accessCache:s,ctx:n,appContext:i,propsOptions:r}},o){let l;return!!s[o]||e!==V&&j(e,o)||xs(t,o)||(l=r[0])&&j(l,o)||j(n,o)||j(Ct,o)||j(i.config.globalProperties,o)},defineProperty(e,t,s){return s.get!=null?e._.accessCache[t]=0:j(s,"value")&&this.set(e,t,s.value,null),Reflect.defineProperty(e,t,s)}};function hn(e){return I(e)?e.reduce((t,s)=>(t[s]=null,t),{}):e}let Ds=!0;function to(e){const t=xi(e),s=e.proxy,n=e.ctx;Ds=!1,t.beforeCreate&&pn(t.beforeCreate,e,"bc");const{data:i,computed:r,methods:o,watch:l,provide:f,inject:h,created:a,beforeMount:p,mounted:T,beforeUpdate:C,updated:D,activated:F,deactivated:z,beforeDestroy:L,beforeUnmount:$,destroyed:J,unmounted:P,render:G,renderTracked:je,renderTriggered:he,errorCaptured:Ne,serverPrefetch:jt,expose:Be,inheritAttrs:ut,components:Nt,directives:Ht,filters:hs}=t;if(h&&so(h,n,null),o)for(const k in o){const U=o[k];R(U)&&(n[k]=U.bind(s))}if(i){const k=i.call(s,s);W(k)&&(e.data=qs(k))}if(Ds=!0,r)for(const k in r){const U=r[k],We=R(U)?U.bind(s,s):R(U.get)?U.get.bind(s,s):we,Lt=!R(U)&&R(U.set)?U.set.bind(s):we,ke=Wo({get:We,set:Lt});Object.defineProperty(n,k,{enumerable:!0,configurable:!0,get:()=>ke.value,set:pe=>ke.value=pe})}if(l)for(const k in l)_i(l[k],n,s,k);if(f){const k=R(f)?f.call(s):f;Reflect.ownKeys(k).forEach(U=>{co(U,k[U])})}a&&pn(a,e,"c");function X(k,U){I(U)?U.forEach(We=>k(We.bind(s))):U&&k(U.bind(s))}if(X(Wr,p),X(kr,T),X($r,C),X(Jr,D),X(Ur,F),X(Kr,z),X(Xr,Ne),X(Yr,je),X(zr,he),X(qr,$),X(yi,P),X(Gr,jt),I(Be))if(Be.length){const k=e.exposed||(e.exposed={});Be.forEach(U=>{Object.defineProperty(k,U,{get:()=>s[U],set:We=>s[U]=We,enumerable:!0})})}else e.exposed||(e.exposed={});G&&e.render===we&&(e.render=G),ut!=null&&(e.inheritAttrs=ut),Nt&&(e.components=Nt),Ht&&(e.directives=Ht),jt&&gi(e)}function so(e,t,s=we){I(e)&&(e=js(e));for(const n in e){const i=e[n];let r;W(i)?"default"in i?r=kt(i.from||n,i.default,!0):r=kt(i.from||n):r=kt(i),se(r)?Object.defineProperty(t,n,{enumerable:!0,configurable:!0,get:()=>r.value,set:o=>r.value=o}):t[n]=r}}function pn(e,t,s){Te(I(e)?e.map(n=>n.bind(t.proxy)):e.bind(t.proxy),t,s)}function _i(e,t,s,n){let i=n.includes(".")?Fi(s,n):()=>s[n];if(q(e)){const r=t[e];R(r)&&ws(i,r)}else if(R(e))ws(i,e.bind(s));else if(W(e))if(I(e))e.forEach(r=>_i(r,t,s,n));else{const r=R(e.handler)?e.handler.bind(s):t[e.handler];R(r)&&ws(i,r,e)}}function xi(e){const t=e.type,{mixins:s,extends:n}=t,{mixins:i,optionsCache:r,config:{optionMergeStrategies:o}}=e.appContext,l=r.get(t);let f;return l?f=l:!i.length&&!s&&!n?f=t:(f={},i.length&&i.forEach(h=>Xt(f,h,o,!0)),Xt(f,t,o)),W(t)&&r.set(t,f),f}function Xt(e,t,s,n=!1){const{mixins:i,extends:r}=t;r&&Xt(e,r,s,!0),i&&i.forEach(o=>Xt(e,o,s,!0));for(const o in t)if(!(n&&o==="expose")){const l=no[o]||s&&s[o];e[o]=l?l(e[o],t[o]):t[o]}return e}const no={data:gn,props:bn,emits:bn,methods:_t,computed:_t,beforeCreate:Z,created:Z,beforeMount:Z,mounted:Z,beforeUpdate:Z,updated:Z,beforeDestroy:Z,beforeUnmount:Z,destroyed:Z,unmounted:Z,activated:Z,deactivated:Z,errorCaptured:Z,serverPrefetch:Z,components:_t,directives:_t,watch:ro,provide:gn,inject:io};function gn(e,t){return t?e?function(){return ne(R(e)?e.call(this,this):e,R(t)?t.call(this,this):t)}:t:e}function io(e,t){return _t(js(e),js(t))}function js(e){if(I(e)){const t={};for(let s=0;s<e.length;s++)t[e[s]]=e[s];return t}return e}function Z(e,t){return e?[...new Set([].concat(e,t))]:t}function _t(e,t){return e?ne(Object.create(null),e,t):t}function bn(e,t){return e?I(e)&&I(t)?[...new Set([...e,...t])]:ne(Object.create(null),hn(e),hn(t??{})):t}function ro(e,t){if(!e)return t;if(!t)return e;const s=ne(Object.create(null),e);for(const n in t)s[n]=Z(e[n],t[n]);return s}function vi(){return{app:null,config:{isNativeTag:qi,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let oo=0;function lo(e,t){return function(n,i=null){R(n)||(n=ne({},n)),i!=null&&!W(i)&&(i=null);const r=vi(),o=new WeakSet,l=[];let f=!1;const h=r.app={_uid:oo++,_component:n,_props:i,_container:null,_context:r,_instance:null,version:ko,get config(){return r.config},set config(a){},use(a,...p){return o.has(a)||(a&&R(a.install)?(o.add(a),a.install(h,...p)):R(a)&&(o.add(a),a(h,...p))),h},mixin(a){return r.mixins.includes(a)||r.mixins.push(a),h},component(a,p){return p?(r.components[a]=p,h):r.components[a]},directive(a,p){return p?(r.directives[a]=p,h):r.directives[a]},mount(a,p,T){if(!f){const C=h._ceVNode||Re(n,i);return C.appContext=r,T===!0?T="svg":T===!1&&(T=void 0),e(C,a,T),f=!0,h._container=a,a.__vue_app__=h,ds(C.component)}},onUnmount(a){l.push(a)},unmount(){f&&(Te(l,h._instance,16),e(null,h._container),delete h._container.__vue_app__)},provide(a,p){return r.provides[a]=p,h},runWithContext(a){const p=lt;lt=h;try{return a()}finally{lt=p}}};return h}}let lt=null;function co(e,t){if(te){let s=te.provides;const n=te.parent&&te.parent.provides;n===s&&(s=te.provides=Object.create(n)),s[e]=t}}function kt(e,t,s=!1){const n=Ho();if(n||lt){let i=lt?lt._context.provides:n?n.parent==null||n.ce?n.vnode.appContext&&n.vnode.appContext.provides:n.parent.provides:void 0;if(i&&e in i)return i[e];if(arguments.length>1)return s&&R(t)?t.call(n&&n.proxy):t}}const wi={},Si=()=>Object.create(wi),Ti=e=>Object.getPrototypeOf(e)===wi;function fo(e,t,s,n=!1){const i={},r=Si();e.propsDefaults=Object.create(null),Ci(e,t,i,r);for(const o in e.propsOptions[0])o in i||(i[o]=void 0);s?e.props=n?i:Cr(i):e.type.props?e.props=i:e.props=r,e.attrs=r}function uo(e,t,s,n){const{props:i,attrs:r,vnode:{patchFlag:o}}=e,l=H(i),[f]=e.propsOptions;let h=!1;if((n||o>0)&&!(o&16)){if(o&8){const a=e.vnode.dynamicProps;for(let p=0;p<a.length;p++){let T=a[p];if(us(e.emitsOptions,T))continue;const C=t[T];if(f)if(j(r,T))C!==r[T]&&(r[T]=C,h=!0);else{const D=Ue(T);i[D]=Ns(f,l,D,C,e,!1)}else C!==r[T]&&(r[T]=C,h=!0)}}}else{Ci(e,t,i,r)&&(h=!0);let a;for(const p in l)(!t||!j(t,p)&&((a=Qe(p))===p||!j(t,a)))&&(f?s&&(s[p]!==void 0||s[a]!==void 0)&&(i[p]=Ns(f,l,p,void 0,e,!0)):delete i[p]);if(r!==l)for(const p in r)(!t||!j(t,p))&&(delete r[p],h=!0)}h&&Pe(e.attrs,"set","")}function Ci(e,t,s,n){const[i,r]=e.propsOptions;let o=!1,l;if(t)for(let f in t){if(xt(f))continue;const h=t[f];let a;i&&j(i,a=Ue(f))?!r||!r.includes(a)?s[a]=h:(l||(l={}))[a]=h:us(e.emitsOptions,f)||(!(f in n)||h!==n[f])&&(n[f]=h,o=!0)}if(r){const f=H(s),h=l||V;for(let a=0;a<r.length;a++){const p=r[a];s[p]=Ns(i,f,p,h[p],e,!j(h,p))}}return o}function Ns(e,t,s,n,i,r){const o=e[s];if(o!=null){const l=j(o,"default");if(l&&n===void 0){const f=o.default;if(o.type!==Function&&!o.skipFactory&&R(f)){const{propsDefaults:h}=i;if(s in h)n=h[s];else{const a=Dt(i);n=h[s]=f.call(null,t),a()}}else n=f;i.ce&&i.ce._setProp(s,n)}o[0]&&(r&&!l?n=!1:o[1]&&(n===""||n===Qe(s))&&(n=!0))}return n}const ao=new WeakMap;function Oi(e,t,s=!1){const n=s?ao:t.propsCache,i=n.get(e);if(i)return i;const r=e.props,o={},l=[];let f=!1;if(!R(e)){const a=p=>{f=!0;const[T,C]=Oi(p,t,!0);ne(o,T),C&&l.push(...C)};!s&&t.mixins.length&&t.mixins.forEach(a),e.extends&&a(e.extends),e.mixins&&e.mixins.forEach(a)}if(!r&&!f)return W(e)&&n.set(e,nt),nt;if(I(r))for(let a=0;a<r.length;a++){const p=Ue(r[a]);mn(p)&&(o[p]=V)}else if(r)for(const a in r){const p=Ue(a);if(mn(p)){const T=r[a],C=o[p]=I(T)||R(T)?{type:T}:ne({},T),D=C.type;let F=!1,z=!0;if(I(D))for(let L=0;L<D.length;++L){const $=D[L],J=R($)&&$.name;if(J==="Boolean"){F=!0;break}else J==="String"&&(z=!1)}else F=R(D)&&D.name==="Boolean";C[0]=F,C[1]=z,(F||j(C,"default"))&&l.push(p)}}const h=[o,l];return W(e)&&n.set(e,h),h}function mn(e){return e[0]!=="$"&&!xt(e)}const Zs=e=>e==="_"||e==="__"||e==="_ctx"||e==="$stable",Qs=e=>I(e)?e.map(ve):[ve(e)],ho=(e,t,s)=>{if(t._n)return t;const n=Hr((...i)=>Qs(t(...i)),s);return n._c=!1,n},Ei=(e,t,s)=>{const n=e._ctx;for(const i in e){if(Zs(i))continue;const r=e[i];if(R(r))t[i]=ho(i,r,n);else if(r!=null){const o=Qs(r);t[i]=()=>o}}},Ii=(e,t)=>{const s=Qs(t);e.slots.default=()=>s},Pi=(e,t,s)=>{for(const n in t)(s||!Zs(n))&&(e[n]=t[n])},po=(e,t,s)=>{const n=e.slots=Si();if(e.vnode.shapeFlag&32){const i=t.__;i&&Es(n,"__",i,!0);const r=t._;r?(Pi(n,t,s),s&&Es(n,"_",r,!0)):Ei(t,n)}else t&&Ii(e,t)},go=(e,t,s)=>{const{vnode:n,slots:i}=e;let r=!0,o=V;if(n.shapeFlag&32){const l=t._;l?s&&l===1?r=!1:Pi(i,t,s):(r=!t.$stable,Ei(t,i)),o=t}else t&&(Ii(e,t),o={default:1});if(r)for(const l in i)!Zs(l)&&o[l]==null&&delete i[l]},ce=Po;function bo(e){return mo(e)}function mo(e,t){const s=is();s.__VUE__=!0;const{insert:n,remove:i,patchProp:r,createElement:o,createText:l,createComment:f,setText:h,setElementText:a,parentNode:p,nextSibling:T,setScopeId:C=we,insertStaticContent:D}=e,F=(c,u,d,m=null,g=null,b=null,v=void 0,x=null,_=!!u.dynamicChildren)=>{if(c===u)return;c&&!gt(c,u)&&(m=Vt(c),pe(c,g,b,!0),c=null),u.patchFlag===-2&&(_=!1,u.dynamicChildren=null);const{type:y,ref:E,shapeFlag:w}=u;switch(y){case as:z(c,u,d,m);break;case Ke:L(c,u,d,m);break;case Ss:c==null&&$(u,d,m,v);break;case xe:Nt(c,u,d,m,g,b,v,x,_);break;default:w&1?G(c,u,d,m,g,b,v,x,_):w&6?Ht(c,u,d,m,g,b,v,x,_):(w&64||w&128)&&y.process(c,u,d,m,g,b,v,x,_,dt)}E!=null&&g?St(E,c&&c.ref,b,u||c,!u):E==null&&c&&c.ref!=null&&St(c.ref,null,b,c,!0)},z=(c,u,d,m)=>{if(c==null)n(u.el=l(u.children),d,m);else{const g=u.el=c.el;u.children!==c.children&&h(g,u.children)}},L=(c,u,d,m)=>{c==null?n(u.el=f(u.children||""),d,m):u.el=c.el},$=(c,u,d,m)=>{[c.el,c.anchor]=D(c.children,u,d,m,c.el,c.anchor)},J=({el:c,anchor:u},d,m)=>{let g;for(;c&&c!==u;)g=T(c),n(c,d,m),c=g;n(u,d,m)},P=({el:c,anchor:u})=>{let d;for(;c&&c!==u;)d=T(c),i(c),c=d;i(u)},G=(c,u,d,m,g,b,v,x,_)=>{u.type==="svg"?v="svg":u.type==="math"&&(v="mathml"),c==null?je(u,d,m,g,b,v,x,_):jt(c,u,g,b,v,x,_)},je=(c,u,d,m,g,b,v,x)=>{let _,y;const{props:E,shapeFlag:w,transition:O,dirs:A}=c;if(_=c.el=o(c.type,b,E&&E.is,E),w&8?a(_,c.children):w&16&&Ne(c.children,_,null,m,g,vs(c,b),v,x),A&&$e(c,null,m,"created"),he(_,c,c.scopeId,v,m),E){for(const K in E)K!=="value"&&!xt(K)&&r(_,K,null,E[K],b,m);"value"in E&&r(_,"value",null,E.value,b),(y=E.onVnodeBeforeMount)&&ye(y,m,c)}A&&$e(c,null,m,"beforeMount");const M=yo(g,O);M&&O.beforeEnter(_),n(_,u,d),((y=E&&E.onVnodeMounted)||M||A)&&ce(()=>{y&&ye(y,m,c),M&&O.enter(_),A&&$e(c,null,m,"mounted")},g)},he=(c,u,d,m,g)=>{if(d&&C(c,d),m)for(let b=0;b<m.length;b++)C(c,m[b]);if(g){let b=g.subTree;if(u===b||ji(b.type)&&(b.ssContent===u||b.ssFallback===u)){const v=g.vnode;he(c,v,v.scopeId,v.slotScopeIds,g.parent)}}},Ne=(c,u,d,m,g,b,v,x,_=0)=>{for(let y=_;y<c.length;y++){const E=c[y]=x?Le(c[y]):ve(c[y]);F(null,E,u,d,m,g,b,v,x)}},jt=(c,u,d,m,g,b,v)=>{const x=u.el=c.el;let{patchFlag:_,dynamicChildren:y,dirs:E}=u;_|=c.patchFlag&16;const w=c.props||V,O=u.props||V;let A;if(d&&Je(d,!1),(A=O.onVnodeBeforeUpdate)&&ye(A,d,u,c),E&&$e(u,c,d,"beforeUpdate"),d&&Je(d,!0),(w.innerHTML&&O.innerHTML==null||w.textContent&&O.textContent==null)&&a(x,""),y?Be(c.dynamicChildren,y,x,d,m,vs(u,g),b):v||U(c,u,x,null,d,m,vs(u,g),b,!1),_>0){if(_&16)ut(x,w,O,d,g);else if(_&2&&w.class!==O.class&&r(x,"class",null,O.class,g),_&4&&r(x,"style",w.style,O.style,g),_&8){const M=u.dynamicProps;for(let K=0;K<M.length;K++){const N=M[K],ie=w[N],re=O[N];(re!==ie||N==="value")&&r(x,N,ie,re,g,d)}}_&1&&c.children!==u.children&&a(x,u.children)}else!v&&y==null&&ut(x,w,O,d,g);((A=O.onVnodeUpdated)||E)&&ce(()=>{A&&ye(A,d,u,c),E&&$e(u,c,d,"updated")},m)},Be=(c,u,d,m,g,b,v)=>{for(let x=0;x<u.length;x++){const _=c[x],y=u[x],E=_.el&&(_.type===xe||!gt(_,y)||_.shapeFlag&198)?p(_.el):d;F(_,y,E,null,m,g,b,v,!0)}},ut=(c,u,d,m,g)=>{if(u!==d){if(u!==V)for(const b in u)!xt(b)&&!(b in d)&&r(c,b,u[b],null,g,m);for(const b in d){if(xt(b))continue;const v=d[b],x=u[b];v!==x&&b!=="value"&&r(c,b,x,v,g,m)}"value"in d&&r(c,"value",u.value,d.value,g)}},Nt=(c,u,d,m,g,b,v,x,_)=>{const y=u.el=c?c.el:l(""),E=u.anchor=c?c.anchor:l("");let{patchFlag:w,dynamicChildren:O,slotScopeIds:A}=u;A&&(x=x?x.concat(A):A),c==null?(n(y,d,m),n(E,d,m),Ne(u.children||[],d,E,g,b,v,x,_)):w>0&&w&64&&O&&c.dynamicChildren?(Be(c.dynamicChildren,O,d,g,b,v,x),(u.key!=null||g&&u===g.subTree)&&Ai(c,u,!0)):U(c,u,d,E,g,b,v,x,_)},Ht=(c,u,d,m,g,b,v,x,_)=>{u.slotScopeIds=x,c==null?u.shapeFlag&512?g.ctx.activate(u,d,m,v,_):hs(u,d,m,g,b,v,_):tn(c,u,_)},hs=(c,u,d,m,g,b,v)=>{const x=c.component=No(c,m,g);if(bi(c)&&(x.ctx.renderer=dt),Lo(x,!1,v),x.asyncDep){if(g&&g.registerDep(x,X,v),!c.el){const _=x.subTree=Re(Ke);L(null,_,u,d),c.placeholder=_.el}}else X(x,c,u,d,g,b,v)},tn=(c,u,d)=>{const m=u.component=c.component;if(Eo(c,u,d))if(m.asyncDep&&!m.asyncResolved){k(m,u,d);return}else m.next=u,m.update();else u.el=c.el,m.vnode=u},X=(c,u,d,m,g,b,v)=>{const x=()=>{if(c.isMounted){let{next:w,bu:O,u:A,parent:M,vnode:K}=c;{const be=Ri(c);if(be){w&&(w.el=K.el,k(c,w,v)),be.asyncDep.then(()=>{c.isUnmounted||x()});return}}let N=w,ie;Je(c,!1),w?(w.el=K.el,k(c,w,v)):w=K,O&&Wt(O),(ie=w.props&&w.props.onVnodeBeforeUpdate)&&ye(ie,M,w,K),Je(c,!0);const re=_n(c),ge=c.subTree;c.subTree=re,F(ge,re,p(ge.el),Vt(ge),c,g,b),w.el=re.el,N===null&&Io(c,re.el),A&&ce(A,g),(ie=w.props&&w.props.onVnodeUpdated)&&ce(()=>ye(ie,M,w,K),g)}else{let w;const{el:O,props:A}=u,{bm:M,m:K,parent:N,root:ie,type:re}=c,ge=Tt(u);Je(c,!1),M&&Wt(M),!ge&&(w=A&&A.onVnodeBeforeMount)&&ye(w,N,u),Je(c,!0);{ie.ce&&ie.ce._def.shadowRoot!==!1&&ie.ce._injectChildStyle(re);const be=c.subTree=_n(c);F(null,be,d,m,c,g,b),u.el=be.el}if(K&&ce(K,g),!ge&&(w=A&&A.onVnodeMounted)){const be=u;ce(()=>ye(w,N,be),g)}(u.shapeFlag&256||N&&Tt(N.vnode)&&N.vnode.shapeFlag&256)&&c.a&&ce(c.a,g),c.isMounted=!0,u=d=m=null}};c.scope.on();const _=c.effect=new qn(x);c.scope.off();const y=c.update=_.run.bind(_),E=c.job=_.runIfDirty.bind(_);E.i=c,E.id=c.uid,_.scheduler=()=>Ys(E),Je(c,!0),y()},k=(c,u,d)=>{u.component=c;const m=c.vnode.props;c.vnode=u,c.next=null,uo(c,u.props,m,d),go(c,u.children,d),Me(),dn(c),Fe()},U=(c,u,d,m,g,b,v,x,_=!1)=>{const y=c&&c.children,E=c?c.shapeFlag:0,w=u.children,{patchFlag:O,shapeFlag:A}=u;if(O>0){if(O&128){Lt(y,w,d,m,g,b,v,x,_);return}else if(O&256){We(y,w,d,m,g,b,v,x,_);return}}A&8?(E&16&&at(y,g,b),w!==y&&a(d,w)):E&16?A&16?Lt(y,w,d,m,g,b,v,x,_):at(y,g,b,!0):(E&8&&a(d,""),A&16&&Ne(w,d,m,g,b,v,x,_))},We=(c,u,d,m,g,b,v,x,_)=>{c=c||nt,u=u||nt;const y=c.length,E=u.length,w=Math.min(y,E);let O;for(O=0;O<w;O++){const A=u[O]=_?Le(u[O]):ve(u[O]);F(c[O],A,d,null,g,b,v,x,_)}y>E?at(c,g,b,!0,!1,w):Ne(u,d,m,g,b,v,x,_,w)},Lt=(c,u,d,m,g,b,v,x,_)=>{let y=0;const E=u.length;let w=c.length-1,O=E-1;for(;y<=w&&y<=O;){const A=c[y],M=u[y]=_?Le(u[y]):ve(u[y]);if(gt(A,M))F(A,M,d,null,g,b,v,x,_);else break;y++}for(;y<=w&&y<=O;){const A=c[w],M=u[O]=_?Le(u[O]):ve(u[O]);if(gt(A,M))F(A,M,d,null,g,b,v,x,_);else break;w--,O--}if(y>w){if(y<=O){const A=O+1,M=A<E?u[A].el:m;for(;y<=O;)F(null,u[y]=_?Le(u[y]):ve(u[y]),d,M,g,b,v,x,_),y++}}else if(y>O)for(;y<=w;)pe(c[y],g,b,!0),y++;else{const A=y,M=y,K=new Map;for(y=M;y<=O;y++){const le=u[y]=_?Le(u[y]):ve(u[y]);le.key!=null&&K.set(le.key,y)}let N,ie=0;const re=O-M+1;let ge=!1,be=0;const ht=new Array(re);for(y=0;y<re;y++)ht[y]=0;for(y=A;y<=w;y++){const le=c[y];if(ie>=re){pe(le,g,b,!0);continue}let me;if(le.key!=null)me=K.get(le.key);else for(N=M;N<=O;N++)if(ht[N-M]===0&&gt(le,u[N])){me=N;break}me===void 0?pe(le,g,b,!0):(ht[me-M]=y+1,me>=be?be=me:ge=!0,F(le,u[me],d,null,g,b,v,x,_),ie++)}const rn=ge?_o(ht):nt;for(N=rn.length-1,y=re-1;y>=0;y--){const le=M+y,me=u[le],on=u[le+1],ln=le+1<E?on.el||on.placeholder:m;ht[y]===0?F(null,me,d,ln,g,b,v,x,_):ge&&(N<0||y!==rn[N]?ke(me,d,ln,2):N--)}}},ke=(c,u,d,m,g=null)=>{const{el:b,type:v,transition:x,children:_,shapeFlag:y}=c;if(y&6){ke(c.component.subTree,u,d,m);return}if(y&128){c.suspense.move(u,d,m);return}if(y&64){v.move(c,u,d,dt);return}if(v===xe){n(b,u,d);for(let w=0;w<_.length;w++)ke(_[w],u,d,m);n(c.anchor,u,d);return}if(v===Ss){J(c,u,d);return}if(m!==2&&y&1&&x)if(m===0)x.beforeEnter(b),n(b,u,d),ce(()=>x.enter(b),g);else{const{leave:w,delayLeave:O,afterLeave:A}=x,M=()=>{c.ctx.isUnmounted?i(b):n(b,u,d)},K=()=>{w(b,()=>{M(),A&&A()})};O?O(b,M,K):K()}else n(b,u,d)},pe=(c,u,d,m=!1,g=!1)=>{const{type:b,props:v,ref:x,children:_,dynamicChildren:y,shapeFlag:E,patchFlag:w,dirs:O,cacheIndex:A}=c;if(w===-2&&(g=!1),x!=null&&(Me(),St(x,null,d,c,!0),Fe()),A!=null&&(u.renderCache[A]=void 0),E&256){u.ctx.deactivate(c);return}const M=E&1&&O,K=!Tt(c);let N;if(K&&(N=v&&v.onVnodeBeforeUnmount)&&ye(N,u,c),E&6)Ji(c.component,d,m);else{if(E&128){c.suspense.unmount(d,m);return}M&&$e(c,null,u,"beforeUnmount"),E&64?c.type.remove(c,u,d,dt,m):y&&!y.hasOnce&&(b!==xe||w>0&&w&64)?at(y,u,d,!1,!0):(b===xe&&w&384||!g&&E&16)&&at(_,u,d),m&&sn(c)}(K&&(N=v&&v.onVnodeUnmounted)||M)&&ce(()=>{N&&ye(N,u,c),M&&$e(c,null,u,"unmounted")},d)},sn=c=>{const{type:u,el:d,anchor:m,transition:g}=c;if(u===xe){$i(d,m);return}if(u===Ss){P(c);return}const b=()=>{i(d),g&&!g.persisted&&g.afterLeave&&g.afterLeave()};if(c.shapeFlag&1&&g&&!g.persisted){const{leave:v,delayLeave:x}=g,_=()=>v(d,b);x?x(c.el,b,_):_()}else b()},$i=(c,u)=>{let d;for(;c!==u;)d=T(c),i(c),c=d;i(u)},Ji=(c,u,d)=>{const{bum:m,scope:g,job:b,subTree:v,um:x,m:_,a:y,parent:E,slots:{__:w}}=c;yn(_),yn(y),m&&Wt(m),E&&I(w)&&w.forEach(O=>{E.renderCache[O]=void 0}),g.stop(),b&&(b.flags|=8,pe(v,c,u,d)),x&&ce(x,u),ce(()=>{c.isUnmounted=!0},u),u&&u.pendingBranch&&!u.isUnmounted&&c.asyncDep&&!c.asyncResolved&&c.suspenseId===u.pendingId&&(u.deps--,u.deps===0&&u.resolve())},at=(c,u,d,m=!1,g=!1,b=0)=>{for(let v=b;v<c.length;v++)pe(c[v],u,d,m,g)},Vt=c=>{if(c.shapeFlag&6)return Vt(c.component.subTree);if(c.shapeFlag&128)return c.suspense.next();const u=T(c.anchor||c.el),d=u&&u[Lr];return d?T(d):u};let ps=!1;const nn=(c,u,d)=>{c==null?u._vnode&&pe(u._vnode,null,null,!0):F(u._vnode||null,c,u,null,null,null,d),u._vnode=c,ps||(ps=!0,dn(),di(),ps=!1)},dt={p:F,um:pe,m:ke,r:sn,mt:hs,mc:Ne,pc:U,pbc:Be,n:Vt,o:e};return{render:nn,hydrate:void 0,createApp:lo(nn)}}function vs({type:e,props:t},s){return s==="svg"&&e==="foreignObject"||s==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:s}function Je({effect:e,job:t},s){s?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function yo(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function Ai(e,t,s=!1){const n=e.children,i=t.children;if(I(n)&&I(i))for(let r=0;r<n.length;r++){const o=n[r];let l=i[r];l.shapeFlag&1&&!l.dynamicChildren&&((l.patchFlag<=0||l.patchFlag===32)&&(l=i[r]=Le(i[r]),l.el=o.el),!s&&l.patchFlag!==-2&&Ai(o,l)),l.type===as&&(l.el=o.el),l.type===Ke&&!l.el&&(l.el=o.el)}}function _o(e){const t=e.slice(),s=[0];let n,i,r,o,l;const f=e.length;for(n=0;n<f;n++){const h=e[n];if(h!==0){if(i=s[s.length-1],e[i]<h){t[n]=i,s.push(n);continue}for(r=0,o=s.length-1;r<o;)l=r+o>>1,e[s[l]]<h?r=l+1:o=l;h<e[s[r]]&&(r>0&&(t[n]=s[r-1]),s[r]=n)}}for(r=s.length,o=s[r-1];r-- >0;)s[r]=o,o=t[o];return s}function Ri(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:Ri(t)}function yn(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const xo=Symbol.for("v-scx"),vo=()=>kt(xo);function ws(e,t,s){return Mi(e,t,s)}function Mi(e,t,s=V){const{immediate:n,deep:i,flush:r,once:o}=s,l=ne({},s),f=t&&n||!t&&r!=="post";let h;if(Rt){if(r==="sync"){const C=vo();h=C.__watcherHandles||(C.__watcherHandles=[])}else if(!f){const C=()=>{};return C.stop=we,C.resume=we,C.pause=we,C}}const a=te;l.call=(C,D,F)=>Te(C,a,D,F);let p=!1;r==="post"?l.scheduler=C=>{ce(C,a&&a.suspense)}:r!=="sync"&&(p=!0,l.scheduler=(C,D)=>{D?C():Ys(C)}),l.augmentJob=C=>{t&&(C.flags|=4),p&&(C.flags|=2,a&&(C.id=a.uid,C.i=a))};const T=Mr(e,t,l);return Rt&&(h?h.push(T):f&&T()),T}function wo(e,t,s){const n=this.proxy,i=q(e)?e.includes(".")?Fi(n,e):()=>n[e]:e.bind(n,n);let r;R(t)?r=t:(r=t.handler,s=t);const o=Dt(this),l=Mi(i,r.bind(n),s);return o(),l}function Fi(e,t){const s=t.split(".");return()=>{let n=e;for(let i=0;i<s.length&&n;i++)n=n[s[i]];return n}}const So=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${Ue(t)}Modifiers`]||e[`${Qe(t)}Modifiers`];function To(e,t,...s){if(e.isUnmounted)return;const n=e.vnode.props||V;let i=s;const r=t.startsWith("update:"),o=r&&So(n,t.slice(7));o&&(o.trim&&(i=s.map(a=>q(a)?a.trim():a)),o.number&&(i=s.map(Is)));let l,f=n[l=gs(t)]||n[l=gs(Ue(t))];!f&&r&&(f=n[l=gs(Qe(t))]),f&&Te(f,e,6,i);const h=n[l+"Once"];if(h){if(!e.emitted)e.emitted={};else if(e.emitted[l])return;e.emitted[l]=!0,Te(h,e,6,i)}}function Di(e,t,s=!1){const n=t.emitsCache,i=n.get(e);if(i!==void 0)return i;const r=e.emits;let o={},l=!1;if(!R(e)){const f=h=>{const a=Di(h,t,!0);a&&(l=!0,ne(o,a))};!s&&t.mixins.length&&t.mixins.forEach(f),e.extends&&f(e.extends),e.mixins&&e.mixins.forEach(f)}return!r&&!l?(W(e)&&n.set(e,null),null):(I(r)?r.forEach(f=>o[f]=null):ne(o,r),W(e)&&n.set(e,o),o)}function us(e,t){return!e||!ts(t)?!1:(t=t.slice(2).replace(/Once$/,""),j(e,t[0].toLowerCase()+t.slice(1))||j(e,Qe(t))||j(e,t))}function _n(e){const{type:t,vnode:s,proxy:n,withProxy:i,propsOptions:[r],slots:o,attrs:l,emit:f,render:h,renderCache:a,props:p,data:T,setupState:C,ctx:D,inheritAttrs:F}=e,z=Yt(e);let L,$;try{if(s.shapeFlag&4){const P=i||n,G=P;L=ve(h.call(G,P,a,p,C,T,D)),$=l}else{const P=t;L=ve(P.length>1?P(p,{attrs:l,slots:o,emit:f}):P(p,null)),$=t.props?l:Co(l)}}catch(P){Ot.length=0,cs(P,e,1),L=Re(Ke)}let J=L;if($&&F!==!1){const P=Object.keys($),{shapeFlag:G}=J;P.length&&G&7&&(r&&P.some(Us)&&($=Oo($,r)),J=ft(J,$,!1,!0))}return s.dirs&&(J=ft(J,null,!1,!0),J.dirs=J.dirs?J.dirs.concat(s.dirs):s.dirs),s.transition&&Xs(J,s.transition),L=J,Yt(z),L}const Co=e=>{let t;for(const s in e)(s==="class"||s==="style"||ts(s))&&((t||(t={}))[s]=e[s]);return t},Oo=(e,t)=>{const s={};for(const n in e)(!Us(n)||!(n.slice(9)in t))&&(s[n]=e[n]);return s};function Eo(e,t,s){const{props:n,children:i,component:r}=e,{props:o,children:l,patchFlag:f}=t,h=r.emitsOptions;if(t.dirs||t.transition)return!0;if(s&&f>=0){if(f&1024)return!0;if(f&16)return n?xn(n,o,h):!!o;if(f&8){const a=t.dynamicProps;for(let p=0;p<a.length;p++){const T=a[p];if(o[T]!==n[T]&&!us(h,T))return!0}}}else return(i||l)&&(!l||!l.$stable)?!0:n===o?!1:n?o?xn(n,o,h):!0:!!o;return!1}function xn(e,t,s){const n=Object.keys(t);if(n.length!==Object.keys(e).length)return!0;for(let i=0;i<n.length;i++){const r=n[i];if(t[r]!==e[r]&&!us(s,r))return!0}return!1}function Io({vnode:e,parent:t},s){for(;t;){const n=t.subTree;if(n.suspense&&n.suspense.activeBranch===e&&(n.el=e.el),n===e)(e=t.vnode).el=s,t=t.parent;else break}}const ji=e=>e.__isSuspense;function Po(e,t){t&&t.pendingBranch?I(e)?t.effects.push(...e):t.effects.push(e):Nr(e)}const xe=Symbol.for("v-fgt"),as=Symbol.for("v-txt"),Ke=Symbol.for("v-cmt"),Ss=Symbol.for("v-stc"),Ot=[];let fe=null;function Ee(e=!1){Ot.push(fe=e?null:[])}function Ao(){Ot.pop(),fe=Ot[Ot.length-1]||null}let At=1;function vn(e,t=!1){At+=e,e<0&&fe&&t&&(fe.hasOnce=!0)}function Ni(e){return e.dynamicChildren=At>0?fe||nt:null,Ao(),At>0&&fe&&fe.push(e),e}function qe(e,t,s,n,i,r){return Ni(S(e,t,s,n,i,r,!0))}function Hi(e,t,s,n,i){return Ni(Re(e,t,s,n,i,!0))}function Li(e){return e?e.__v_isVNode===!0:!1}function gt(e,t){return e.type===t.type&&e.key===t.key}const Vi=({key:e})=>e??null,$t=({ref:e,ref_key:t,ref_for:s})=>(typeof e=="number"&&(e=""+e),e!=null?q(e)||se(e)||R(e)?{i:ue,r:e,k:t,f:!!s}:e:null);function S(e,t=null,s=null,n=0,i=null,r=e===xe?0:1,o=!1,l=!1){const f={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&Vi(t),ref:t&&$t(t),scopeId:pi,slotScopeIds:null,children:s,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:r,patchFlag:n,dynamicProps:i,dynamicChildren:null,appContext:null,ctx:ue};return l?(en(f,s),r&128&&e.normalize(f)):s&&(f.shapeFlag|=q(s)?8:16),At>0&&!o&&fe&&(f.patchFlag>0||r&6)&&f.patchFlag!==32&&fe.push(f),f}const Re=Ro;function Ro(e,t=null,s=null,n=0,i=null,r=!1){if((!e||e===Zr)&&(e=Ke),Li(e)){const l=ft(e,t,!0);return s&&en(l,s),At>0&&!r&&fe&&(l.shapeFlag&6?fe[fe.indexOf(e)]=l:fe.push(l)),l.patchFlag=-2,l}if(Bo(e)&&(e=e.__vccOpts),t){t=Mo(t);let{class:l,style:f}=t;l&&!q(l)&&(t.class=Ve(l)),W(f)&&(zs(f)&&!I(f)&&(f=ne({},f)),t.style=rs(f))}const o=q(e)?1:ji(e)?128:Vr(e)?64:W(e)?4:R(e)?2:0;return S(e,t,s,n,i,o,r,!0)}function Mo(e){return e?zs(e)||Ti(e)?ne({},e):e:null}function ft(e,t,s=!1,n=!1){const{props:i,ref:r,patchFlag:o,children:l,transition:f}=e,h=t?Fo(i||{},t):i,a={__v_isVNode:!0,__v_skip:!0,type:e.type,props:h,key:h&&Vi(h),ref:t&&t.ref?s&&r?I(r)?r.concat($t(t)):[r,$t(t)]:$t(t):r,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:l,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==xe?o===-1?16:o|16:o,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:f,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&ft(e.ssContent),ssFallback:e.ssFallback&&ft(e.ssFallback),placeholder:e.placeholder,el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return f&&n&&Xs(a,f.clone(a)),a}function Oe(e=" ",t=0){return Re(as,null,e,t)}function wn(e="",t=!1){return t?(Ee(),Hi(Ke,null,e)):Re(Ke,null,e)}function ve(e){return e==null||typeof e=="boolean"?Re(Ke):I(e)?Re(xe,null,e.slice()):Li(e)?Le(e):Re(as,null,String(e))}function Le(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:ft(e)}function en(e,t){let s=0;const{shapeFlag:n}=e;if(t==null)t=null;else if(I(t))s=16;else if(typeof t=="object")if(n&65){const i=t.default;i&&(i._c&&(i._d=!1),en(e,i()),i._c&&(i._d=!0));return}else{s=32;const i=t._;!i&&!Ti(t)?t._ctx=ue:i===3&&ue&&(ue.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else R(t)?(t={default:t,_ctx:ue},s=32):(t=String(t),n&64?(s=16,t=[Oe(t)]):s=8);e.children=t,e.shapeFlag|=s}function Fo(...e){const t={};for(let s=0;s<e.length;s++){const n=e[s];for(const i in n)if(i==="class")t.class!==n.class&&(t.class=Ve([t.class,n.class]));else if(i==="style")t.style=rs([t.style,n.style]);else if(ts(i)){const r=t[i],o=n[i];o&&r!==o&&!(I(r)&&r.includes(o))&&(t[i]=r?[].concat(r,o):o)}else i!==""&&(t[i]=n[i])}return t}function ye(e,t,s,n=null){Te(e,t,7,[s,n])}const Do=vi();let jo=0;function No(e,t,s){const n=e.type,i=(t?t.appContext:e.appContext)||Do,r={uid:jo++,vnode:e,type:n,parent:t,appContext:i,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new rr(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(i.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:Oi(n,i),emitsOptions:Di(n,i),emit:null,emitted:null,propsDefaults:V,inheritAttrs:n.inheritAttrs,ctx:V,data:V,props:V,attrs:V,slots:V,refs:V,setupState:V,setupContext:null,suspense:s,suspenseId:s?s.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return r.ctx={_:r},r.root=t?t.root:r,r.emit=To.bind(null,r),e.ce&&e.ce(r),r}let te=null;const Ho=()=>te||ue;let Zt,Hs;{const e=is(),t=(s,n)=>{let i;return(i=e[s])||(i=e[s]=[]),i.push(n),r=>{i.length>1?i.forEach(o=>o(r)):i[0](r)}};Zt=t("__VUE_INSTANCE_SETTERS__",s=>te=s),Hs=t("__VUE_SSR_SETTERS__",s=>Rt=s)}const Dt=e=>{const t=te;return Zt(e),e.scope.on(),()=>{e.scope.off(),Zt(t)}},Sn=()=>{te&&te.scope.off(),Zt(null)};function Ui(e){return e.vnode.shapeFlag&4}let Rt=!1;function Lo(e,t=!1,s=!1){t&&Hs(t);const{props:n,children:i}=e.vnode,r=Ui(e);fo(e,n,r,t),po(e,i,s||t);const o=r?Vo(e,t):void 0;return t&&Hs(!1),o}function Vo(e,t){const s=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,eo);const{setup:n}=s;if(n){Me();const i=e.setupContext=n.length>1?Ko(e):null,r=Dt(e),o=Ft(n,e,0,[e.props,i]),l=Vn(o);if(Fe(),r(),(l||e.sp)&&!Tt(e)&&gi(e),l){if(o.then(Sn,Sn),t)return o.then(f=>{Tn(e,f)}).catch(f=>{cs(f,e,0)});e.asyncDep=o}else Tn(e,o)}else Ki(e)}function Tn(e,t,s){R(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:W(t)&&(e.setupState=fi(t)),Ki(e)}function Ki(e,t,s){const n=e.type;e.render||(e.render=n.render||we);{const i=Dt(e);Me();try{to(e)}finally{Fe(),i()}}}const Uo={get(e,t){return Y(e,"get",""),e[t]}};function Ko(e){const t=s=>{e.exposed=s||{}};return{attrs:new Proxy(e.attrs,Uo),slots:e.slots,emit:e.emit,expose:t}}function ds(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(fi(Or(e.exposed)),{get(t,s){if(s in t)return t[s];if(s in Ct)return Ct[s](e)},has(t,s){return s in t||s in Ct}})):e.proxy}function Bo(e){return R(e)&&"__vccOpts"in e}const Wo=(e,t)=>Ar(e,t,Rt),ko="3.5.18";/**
* @vue/runtime-dom v3.5.18
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let Ls;const Cn=typeof window<"u"&&window.trustedTypes;if(Cn)try{Ls=Cn.createPolicy("vue",{createHTML:e=>e})}catch{}const Bi=Ls?e=>Ls.createHTML(e):e=>e,$o="http://www.w3.org/2000/svg",Jo="http://www.w3.org/1998/Math/MathML",Ie=typeof document<"u"?document:null,On=Ie&&Ie.createElement("template"),qo={insert:(e,t,s)=>{t.insertBefore(e,s||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,s,n)=>{const i=t==="svg"?Ie.createElementNS($o,e):t==="mathml"?Ie.createElementNS(Jo,e):s?Ie.createElement(e,{is:s}):Ie.createElement(e);return e==="select"&&n&&n.multiple!=null&&i.setAttribute("multiple",n.multiple),i},createText:e=>Ie.createTextNode(e),createComment:e=>Ie.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>Ie.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,s,n,i,r){const o=s?s.previousSibling:t.lastChild;if(i&&(i===r||i.nextSibling))for(;t.insertBefore(i.cloneNode(!0),s),!(i===r||!(i=i.nextSibling)););else{On.innerHTML=Bi(n==="svg"?`<svg>${e}</svg>`:n==="mathml"?`<math>${e}</math>`:e);const l=On.content;if(n==="svg"||n==="mathml"){const f=l.firstChild;for(;f.firstChild;)l.appendChild(f.firstChild);l.removeChild(f)}t.insertBefore(l,s)}return[o?o.nextSibling:t.firstChild,s?s.previousSibling:t.lastChild]}},Go=Symbol("_vtc");function zo(e,t,s){const n=e[Go];n&&(t=(t?[t,...n]:[...n]).join(" ")),t==null?e.removeAttribute("class"):s?e.setAttribute("class",t):e.className=t}const Qt=Symbol("_vod"),Wi=Symbol("_vsh"),En={beforeMount(e,{value:t},{transition:s}){e[Qt]=e.style.display==="none"?"":e.style.display,s&&t?s.beforeEnter(e):bt(e,t)},mounted(e,{value:t},{transition:s}){s&&t&&s.enter(e)},updated(e,{value:t,oldValue:s},{transition:n}){!t!=!s&&(n?t?(n.beforeEnter(e),bt(e,!0),n.enter(e)):n.leave(e,()=>{bt(e,!1)}):bt(e,t))},beforeUnmount(e,{value:t}){bt(e,t)}};function bt(e,t){e.style.display=t?e[Qt]:"none",e[Wi]=!t}const Yo=Symbol(""),Xo=/(^|;)\s*display\s*:/;function Zo(e,t,s){const n=e.style,i=q(s);let r=!1;if(s&&!i){if(t)if(q(t))for(const o of t.split(";")){const l=o.slice(0,o.indexOf(":")).trim();s[l]==null&&Jt(n,l,"")}else for(const o in t)s[o]==null&&Jt(n,o,"");for(const o in s)o==="display"&&(r=!0),Jt(n,o,s[o])}else if(i){if(t!==s){const o=n[Yo];o&&(s+=";"+o),n.cssText=s,r=Xo.test(s)}}else t&&e.removeAttribute("style");Qt in e&&(e[Qt]=r?n.display:"",e[Wi]&&(n.display="none"))}const In=/\s*!important$/;function Jt(e,t,s){if(I(s))s.forEach(n=>Jt(e,t,n));else if(s==null&&(s=""),t.startsWith("--"))e.setProperty(t,s);else{const n=Qo(e,t);In.test(s)?e.setProperty(Qe(n),s.replace(In,""),"important"):e[n]=s}}const Pn=["Webkit","Moz","ms"],Ts={};function Qo(e,t){const s=Ts[t];if(s)return s;let n=Ue(t);if(n!=="filter"&&n in e)return Ts[t]=n;n=Bn(n);for(let i=0;i<Pn.length;i++){const r=Pn[i]+n;if(r in e)return Ts[t]=r}return t}const An="http://www.w3.org/1999/xlink";function Rn(e,t,s,n,i,r=nr(t)){n&&t.startsWith("xlink:")?s==null?e.removeAttributeNS(An,t.slice(6,t.length)):e.setAttributeNS(An,t,s):s==null||r&&!Wn(s)?e.removeAttribute(t):e.setAttribute(t,r?"":Se(s)?String(s):s)}function Mn(e,t,s,n,i){if(t==="innerHTML"||t==="textContent"){s!=null&&(e[t]=t==="innerHTML"?Bi(s):s);return}const r=e.tagName;if(t==="value"&&r!=="PROGRESS"&&!r.includes("-")){const l=r==="OPTION"?e.getAttribute("value")||"":e.value,f=s==null?e.type==="checkbox"?"on":"":String(s);(l!==f||!("_value"in e))&&(e.value=f),s==null&&e.removeAttribute(t),e._value=s;return}let o=!1;if(s===""||s==null){const l=typeof e[t];l==="boolean"?s=Wn(s):s==null&&l==="string"?(s="",o=!0):l==="number"&&(s=0,o=!0)}try{e[t]=s}catch{}o&&e.removeAttribute(i||t)}function ze(e,t,s,n){e.addEventListener(t,s,n)}function el(e,t,s,n){e.removeEventListener(t,s,n)}const Fn=Symbol("_vei");function tl(e,t,s,n,i=null){const r=e[Fn]||(e[Fn]={}),o=r[t];if(n&&o)o.value=n;else{const[l,f]=sl(t);if(n){const h=r[t]=rl(n,i);ze(e,l,h,f)}else o&&(el(e,l,o,f),r[t]=void 0)}}const Dn=/(?:Once|Passive|Capture)$/;function sl(e){let t;if(Dn.test(e)){t={};let n;for(;n=e.match(Dn);)e=e.slice(0,e.length-n[0].length),t[n[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):Qe(e.slice(2)),t]}let Cs=0;const nl=Promise.resolve(),il=()=>Cs||(nl.then(()=>Cs=0),Cs=Date.now());function rl(e,t){const s=n=>{if(!n._vts)n._vts=Date.now();else if(n._vts<=s.attached)return;Te(ol(n,s.value),t,5,[n])};return s.value=e,s.attached=il(),s}function ol(e,t){if(I(t)){const s=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{s.call(e),e._stopped=!0},t.map(n=>i=>!i._stopped&&n&&n(i))}else return t}const jn=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,ll=(e,t,s,n,i,r)=>{const o=i==="svg";t==="class"?zo(e,n,o):t==="style"?Zo(e,s,n):ts(t)?Us(t)||tl(e,t,s,n,r):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):cl(e,t,n,o))?(Mn(e,t,n),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&Rn(e,t,n,o,r,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!q(n))?Mn(e,Ue(t),n,r,t):(t==="true-value"?e._trueValue=n:t==="false-value"&&(e._falseValue=n),Rn(e,t,n,o))};function cl(e,t,s,n){if(n)return!!(t==="innerHTML"||t==="textContent"||t in e&&jn(t)&&R(s));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="autocorrect"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const i=e.tagName;if(i==="IMG"||i==="VIDEO"||i==="CANVAS"||i==="SOURCE")return!1}return jn(t)&&q(s)?!1:t in e}const es=e=>{const t=e.props["onUpdate:modelValue"]||!1;return I(t)?s=>Wt(t,s):t};function fl(e){e.target.composing=!0}function Nn(e){const t=e.target;t.composing&&(t.composing=!1,t.dispatchEvent(new Event("input")))}const ct=Symbol("_assign"),Os={created(e,{modifiers:{lazy:t,trim:s,number:n}},i){e[ct]=es(i);const r=n||i.props&&i.props.type==="number";ze(e,t?"change":"input",o=>{if(o.target.composing)return;let l=e.value;s&&(l=l.trim()),r&&(l=Is(l)),e[ct](l)}),s&&ze(e,"change",()=>{e.value=e.value.trim()}),t||(ze(e,"compositionstart",fl),ze(e,"compositionend",Nn),ze(e,"change",Nn))},mounted(e,{value:t}){e.value=t??""},beforeUpdate(e,{value:t,oldValue:s,modifiers:{lazy:n,trim:i,number:r}},o){if(e[ct]=es(o),e.composing)return;const l=(r||e.type==="number")&&!/^0\d/.test(e.value)?Is(e.value):e.value,f=t??"";l!==f&&(document.activeElement===e&&e.type!=="range"&&(n&&t===s||i&&e.value.trim()===f)||(e.value=f))}},ul={deep:!0,created(e,t,s){e[ct]=es(s),ze(e,"change",()=>{const n=e._modelValue,i=al(e),r=e.checked,o=e[ct];if(I(n)){const l=kn(n,i),f=l!==-1;if(r&&!f)o(n.concat(i));else if(!r&&f){const h=[...n];h.splice(l,1),o(h)}}else if(ss(n)){const l=new Set(n);r?l.add(i):l.delete(i),o(l)}else o(ki(e,r))})},mounted:Hn,beforeUpdate(e,t,s){e[ct]=es(s),Hn(e,t,s)}};function Hn(e,{value:t,oldValue:s},n){e._modelValue=t;let i;if(I(t))i=kn(t,n.props.value)>-1;else if(ss(t))i=t.has(n.props.value);else{if(t===s)return;i=os(t,ki(e,!0))}e.checked!==i&&(e.checked=i)}function al(e){return"_value"in e?e._value:e.value}function ki(e,t){const s=t?"_trueValue":"_falseValue";return s in e?e[s]:t}const dl=["ctrl","shift","alt","meta"],hl={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&e.button!==0,middle:e=>"button"in e&&e.button!==1,right:e=>"button"in e&&e.button!==2,exact:(e,t)=>dl.some(s=>e[`${s}Key`]&&!t.includes(s))},mt=(e,t)=>{const s=e._withMods||(e._withMods={}),n=t.join(".");return s[n]||(s[n]=(i,...r)=>{for(let o=0;o<t.length;o++){const l=hl[t[o]];if(l&&l(i,t))return}return e(i,...r)})},pl=ne({patchProp:ll},qo);let Ln;function gl(){return Ln||(Ln=bo(pl))}const bl=(...e)=>{const t=gl().createApp(...e),{mount:s}=t;return t.mount=n=>{const i=yl(n);if(!i)return;const r=t._component;!R(r)&&!r.render&&!r.template&&(r.template=i.innerHTML),i.nodeType===1&&(i.textContent="");const o=s(i,!1,ml(i));return i instanceof Element&&(i.removeAttribute("v-cloak"),i.setAttribute("data-v-app","")),o},t};function ml(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function yl(e){return q(e)?document.querySelector(e):e}const _l=(e,t)=>{const s=e.__vccOpts||e;for(const[n,i]of t)s[n]=i;return s},xl={name:"Dashboard",data(){return{tab:"chat",connected:!1,recording:!1,connectionStatus:"disconnected",chatInput:"",ttsInput:"",messages:[],sessionId:0,useStun:!1,videoScale:100,pc:null,recognition:null,showChat:!1,showSettings:!1}},computed:{statusText(){return{disconnected:"未连接",connecting:"连接中...",connected:"已连接"}[this.connectionStatus]||"未连接"},videoHeight(){return`${Math.round(480*(this.videoScale/100))}px`},tabCls(){return"px-3 py-2 rounded-lg border border-white/10 hover:bg-white/10 transition-colors"},activeTabCls(){return"px-3 py-2 rounded-lg bg-primary text-white shadow"}},mounted(){const e=window.SpeechRecognition||window.webkitSpeechRecognition;e&&(this.recognition=new e,this.recognition.continuous=!0,this.recognition.interimResults=!0,this.recognition.lang="zh-CN",this.recognition.onresult=t=>{let s="",n="";for(let i=t.resultIndex;i<t.results.length;++i)t.results[i].isFinal?n+=t.results[i][0].transcript:s+=t.results[i][0].transcript;s&&(this.chatInput=s),n&&(this.chatInput=n)},this.recognition.onerror=t=>console.error("语音识别错误:",t.error))},methods:{async onStart(){try{this.connectionStatus="connecting";const e={sdpSemantics:"unified-plan"};this.useStun&&(e.iceServers=[{urls:["stun:stun.l.google.com:19302"]}]),this.pc=new RTCPeerConnection(e),this.pc.addEventListener("track",i=>{i.track.kind==="video"&&(this.$refs.videoEl.srcObject=i.streams[0])}),this.pc.addTransceiver("video",{direction:"recvonly"}),this.pc.addTransceiver("audio",{direction:"recvonly"});const t=await this.pc.createOffer();await this.pc.setLocalDescription(t),await new Promise(i=>{if(this.pc.iceGatheringState==="complete")return i();const r=()=>{this.pc.iceGatheringState==="complete"&&(this.pc.removeEventListener("icegatheringstatechange",r),i())};this.pc.addEventListener("icegatheringstatechange",r)});const s=await fetch("/offer",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({sdp:this.pc.localDescription.sdp,type:this.pc.localDescription.type})});if(!s.ok)throw new Error("服务器响应错误");const n=await s.json();this.sessionId=n.sessionid,await this.pc.setRemoteDescription(n),this.connected=!0,this.connectionStatus="connected"}catch(e){console.error(e),this.connectionStatus="disconnected",this.connected=!1}},onStop(){this.pc&&setTimeout(()=>this.pc.close(),500),this.connected=!1,this.connectionStatus="disconnected"},async startRecord(){try{(await fetch("/record",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({type:"start_record",sessionid:this.sessionId})})).ok&&(this.recording=!0)}catch(e){console.error(e)}},async stopRecord(){try{(await fetch("/record",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({type:"end_record",sessionid:this.sessionId})})).ok&&(this.recording=!1)}catch(e){console.error(e)}},async sendChat(){const e=(this.chatInput||"").trim();if(e){this.messages.push({role:"user",text:e}),this.$nextTick(()=>{const t=this.$refs.chatBox;t&&(t.scrollTop=t.scrollHeight)});try{await fetch("/human",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({text:e,type:"chat",interrupt:!0,sessionid:this.sessionId})})}catch(t){console.error(t)}this.chatInput=""}},async sendTTS(){const e=(this.ttsInput||"").trim();if(e){try{await fetch("/human",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({text:e,type:"echo",interrupt:!0,sessionid:this.sessionId})}),this.messages.push({role:"system",text:`已发送朗读请求: "${e}"`}),this.$nextTick(()=>{const t=this.$refs.chatBox;t&&(t.scrollTop=t.scrollHeight)})}catch(t){console.error(t)}this.ttsInput=""}},pressToTalk(e){if(this.recognition){if(e&&!this.recording){this.recording=!0;try{this.recognition.start()}catch{}}else if(!e&&this.recording){this.recording=!1;try{this.recognition.stop()}catch{}setTimeout(()=>{const t=(this.chatInput||"").trim();t&&(this.messages.push({role:"user",text:t}),fetch("/human",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({text:t,type:"chat",interrupt:!0,sessionid:this.sessionId})}),this.chatInput="")},300)}}}}},vl={class:"min-h-screen bg-gradient-to-b from-slate-900 via-slate-800 to-slate-900 text-white"},wl={class:"max-w-7xl mx-auto p-6"},Sl={class:"flex items-center justify-between mb-6"},Tl={class:"flex items-center gap-2 text-sm"},Cl={class:"opacity-80"},Ol={class:"max-w-4xl mx-auto"},El={class:"rounded-2xl overflow-hidden bg-gradient-to-b from-slate-900 to-black border border-white/10 shadow-[0_10px_40px_-10px_rgba(0,0,0,0.6)]"},Il={ref:"videoEl",autoplay:"",playsinline:"",class:"w-full h-full object-contain pointer-events-none"},Pl={class:"px-4 mt-4"},Al={class:"max-w-4xl mx-auto rounded-2xl border border-white/10 bg-white/5 backdrop-blur-xl shadow-[0_8px_30px_rgba(0,0,0,0.4)]"},Rl={class:"px-4 py-3 flex items-center justify-between gap-3 text-sm"},Ml={class:"flex items-center gap-2"},Fl=["disabled"],Dl=["disabled"],jl={class:"flex-1 flex items-center justify-center"},Nl={class:"flex items-center gap-2"},Hl={key:0,class:"fixed inset-0 z-50 flex"},Ll={class:"w-full max-w-md h-full bg-slate-900/90 backdrop-blur-xl border-l border-white/10 shadow-2xl"},Vl={class:"p-4 border-b border-white/10 flex items-center justify-between"},Ul={class:"p-4"},Kl={class:"mb-3 flex gap-3"},Bl={class:"space-y-4"},Wl={ref:"chatBox",class:"h-80 overflow-y-auto space-y-3 p-3 rounded-lg bg-slate-900/40 border border-white/10"},kl={class:"space-y-3"},$l={class:"flex gap-2"},Jl={class:"space-y-3"},ql={key:1,class:"fixed inset-0 z-50 flex"},Gl={class:"w-full max-w-md h-full bg-slate-900/90 backdrop-blur-xl border-l border-white/10 shadow-2xl"},zl={class:"p-4 border-b border-white/10 flex items-center justify-between"},Yl={class:"p-4 space-y-6"},Xl={class:"text-sm opacity-80"},Zl={class:"inline-flex items-center gap-2 cursor-pointer"};function Ql(e,t,s,n,i,r){return Ee(),qe("div",vl,[S("div",wl,[S("div",Sl,[t[23]||(t[23]=S("h1",{class:"text-3xl font-semibold"},"livetalking数字人交互平台",-1)),S("div",Tl,[S("span",{class:Ve(["inline-block w-2.5 h-2.5 rounded-full",{"bg-emerald-400":i.connectionStatus==="connected","bg-amber-400":i.connectionStatus==="connecting","bg-rose-400":i.connectionStatus==="disconnected"}])},null,2),S("span",Cl,yt(r.statusText),1)])]),S("div",Ol,[S("div",El,[S("div",{class:"bg-black flex items-center justify-center pointer-events-none select-none",style:rs({height:r.videoHeight})},[S("video",Il,null,512)],4)])]),S("div",Pl,[S("div",Al,[S("div",Rl,[S("div",Ml,[i.connected?(Ee(),qe("button",{key:1,onClick:t[1]||(t[1]=(...o)=>r.onStop&&r.onStop(...o)),class:"px-3 py-2 rounded-lg bg-rose-600 hover:bg-rose-500"},t[25]||(t[25]=[S("i",{class:"bi bi-stop-fill"},null,-1),Oe(" 停止连接 ",-1)]))):(Ee(),qe("button",{key:0,onClick:t[0]||(t[0]=(...o)=>r.onStart&&r.onStart(...o)),class:"px-3 py-2 rounded-lg bg-primary hover:bg-indigo-600"},t[24]||(t[24]=[S("i",{class:"bi bi-play-fill"},null,-1),Oe(" 开始连接 ",-1)]))),S("button",{onClick:t[2]||(t[2]=(...o)=>r.startRecord&&r.startRecord(...o)),disabled:!i.connected||i.recording,class:"px-3 py-2 rounded-lg border border-white/10 hover:bg-white/10 disabled:opacity-50"},t[26]||(t[26]=[S("i",{class:"bi bi-record-fill"},null,-1),Oe(" 开始录制 ",-1)]),8,Fl),S("button",{onClick:t[3]||(t[3]=(...o)=>r.stopRecord&&r.stopRecord(...o)),disabled:!i.connected||!i.recording,class:"px-3 py-2 rounded-lg border border-white/10 hover:bg-white/10 disabled:opacity-50"},t[27]||(t[27]=[S("i",{class:"bi bi-stop-fill"},null,-1),Oe(" 停止录制 ",-1)]),8,Dl)]),S("div",jl,[S("div",{onMousedown:t[4]||(t[4]=mt(o=>r.pressToTalk(!0),["prevent"])),onMouseup:t[5]||(t[5]=mt(o=>r.pressToTalk(!1),["prevent"])),onMouseleave:t[6]||(t[6]=mt(o=>r.pressToTalk(!1),["prevent"])),onTouchstart:t[7]||(t[7]=mt(o=>r.pressToTalk(!0),["prevent"])),onTouchend:t[8]||(t[8]=mt(o=>r.pressToTalk(!1),["prevent"])),class:Ve(["w-14 h-14 rounded-full flex items-center justify-center cursor-pointer transition-all shadow-md ring-1 ring-white/20",i.recording?"bg-rose-600 animate-pulse":"bg-primary hover:bg-indigo-600"])},t[28]||(t[28]=[S("i",{class:"bi bi-mic-fill text-xl"},null,-1)]),34)]),S("div",Nl,[S("button",{onClick:t[9]||(t[9]=o=>i.showChat=!0),class:"px-3 py-2 rounded-lg border border-white/10 hover:bg-white/10"},t[29]||(t[29]=[S("i",{class:"bi bi-chat-dots"},null,-1),Oe(" 交互 ",-1)])),S("button",{onClick:t[10]||(t[10]=o=>i.showSettings=!0),class:"px-3 py-2 rounded-lg border border-white/10 hover:bg-white/10"},t[30]||(t[30]=[S("i",{class:"bi bi-sliders"},null,-1),Oe(" 设置 ",-1)]))])])])]),i.showChat?(Ee(),qe("div",Hl,[S("div",{class:"flex-1",onClick:t[11]||(t[11]=o=>i.showChat=!1)}),S("div",Ll,[S("div",Vl,[t[32]||(t[32]=S("div",{class:"font-medium"},"互动面板",-1)),S("button",{onClick:t[12]||(t[12]=o=>i.showChat=!1),class:"p-2 rounded hover:bg-white/10"},t[31]||(t[31]=[S("i",{class:"bi bi-x-lg"},null,-1)]))]),S("div",Ul,[S("div",Kl,[S("button",{class:Ve(i.tab==="chat"?r.activeTabCls:r.tabCls),onClick:t[13]||(t[13]=o=>i.tab="chat")},"对话模式",2),S("button",{class:Ve(i.tab==="tts"?r.activeTabCls:r.tabCls),onClick:t[14]||(t[14]=o=>i.tab="tts")},"朗读模式",2)]),tt(S("div",Bl,[S("div",Wl,[t[33]||(t[33]=S("div",{class:"p-2 rounded border-l-4 border-emerald-500 bg-emerald-500/10"},'系统: 欢迎使用livetalking，请点击"开始连接"按钮开始对话。',-1)),(Ee(!0),qe(xe,null,Qr(i.messages,(o,l)=>(Ee(),qe("div",{key:l,class:Ve([o.role==="user"?"bg-sky-500/10 border-sky-500":"bg-emerald-500/10 border-emerald-500","p-2 rounded border-l-4"])},yt(o.role==="user"?"您":"数字人")+": "+yt(o.text),3))),128))],512),S("div",kl,[S("div",$l,[tt(S("textarea",{"onUpdate:modelValue":t[15]||(t[15]=o=>i.chatInput=o),rows:"3",placeholder:"输入您想对数字人说的话...",class:"flex-1 p-2 rounded bg-slate-900/40 border border-white/10"},null,512),[[Os,i.chatInput]]),S("button",{onClick:t[16]||(t[16]=(...o)=>r.sendChat&&r.sendChat(...o)),class:"px-4 py-2 rounded-lg bg-primary hover:bg-indigo-600"},"发送")]),t[34]||(t[34]=S("div",{class:"text-center text-xs opacity-70"},"按住底部麦克风可语音输入",-1))])],512),[[En,i.tab==="chat"]]),tt(S("div",Jl,[t[36]||(t[36]=S("label",{class:"text-sm opacity-80"},"输入要朗读的文本",-1)),tt(S("textarea",{"onUpdate:modelValue":t[17]||(t[17]=o=>i.ttsInput=o),rows:"8",placeholder:"输入您想让数字人朗读的文字...",class:"w-full p-2 rounded bg-slate-900/40 border border-white/10"},null,512),[[Os,i.ttsInput]]),S("button",{onClick:t[18]||(t[18]=(...o)=>r.sendTTS&&r.sendTTS(...o)),class:"w-full px-4 py-2 rounded-lg bg-primary hover:bg-indigo-600"},t[35]||(t[35]=[S("i",{class:"bi bi-volume-up"},null,-1),Oe(" 朗读文本",-1)]))],512),[[En,i.tab==="tts"]])])])])):wn("",!0),i.showSettings?(Ee(),qe("div",ql,[S("div",{class:"flex-1",onClick:t[19]||(t[19]=o=>i.showSettings=!1)}),S("div",Gl,[S("div",zl,[t[38]||(t[38]=S("div",{class:"font-medium"},"设置",-1)),S("button",{onClick:t[20]||(t[20]=o=>i.showSettings=!1),class:"p-2 rounded hover:bg-white/10"},t[37]||(t[37]=[S("i",{class:"bi bi-x-lg"},null,-1)]))]),S("div",Yl,[S("div",null,[S("label",Xl,[t[39]||(t[39]=Oe("视频大小调节: ",-1)),S("span",null,yt(i.videoScale)+"%",1)]),tt(S("input",{type:"range",min:"50",max:"150","onUpdate:modelValue":t[21]||(t[21]=o=>i.videoScale=o),class:"w-full"},null,512),[[Os,i.videoScale,void 0,{number:!0}]])]),S("label",Zl,[tt(S("input",{type:"checkbox","onUpdate:modelValue":t[22]||(t[22]=o=>i.useStun=o),class:"accent-indigo-500"},null,512),[[ul,i.useStun]]),t[40]||(t[40]=S("span",null,"使用STUN服务器",-1))])])])])):wn("",!0),t[41]||(t[41]=S("div",{class:"text-center text-slate-400 text-sm mt-8"},"Made with ❤️ by Marstaos | Frontend & Performance Optimization",-1))])])}const ec=_l(xl,[["render",Ql],["__scopeId","data-v-706a5c8a"]]),tc={__name:"App",setup(e){return(t,s)=>(Ee(),Hi(ec))}};bl(tc).mount("#app");
